{"python.defaultInterpreterPath": "${workspaceFolder}/nnunet-gpu-env/Scripts/python.exe", "python.terminal.activateEnvironment": false, "python.terminal.activateEnvInCurrentTerminal": false, "python.envFile": "${workspaceFolder}/.env", "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "files.associations": {"*.py": "python"}, "terminal.integrated.env.windows": {"nnUNet_raw_data_base": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_raw", "nnUNet_preprocessed": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_preprocessed", "RESULTS_FOLDER": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_trained_models", "KMP_DUPLICATE_LIB_OK": "TRUE"}}