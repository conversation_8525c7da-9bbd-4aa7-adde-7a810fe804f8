data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_graduallyTransitionFromCEToDice.py.i
data/nnunet/run/load_pretrained_weights.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_ForceBD.py.i
data/nnunet/utilities/__pycache__/nd_softmax.cpython-39.pyc.i
data/nnunet/postprocessing/consolidate_postprocessing_simple.py.i
data/nnunet/experiment_planning/experiment_planner_baseline_2DUNet.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_FRN.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/pooling_and_convs/__init__.py.i
data/nnunet/preprocessing/__init__.py.i
data/nnunet/training/network_training/competitions_with_custom_Trainers/BraTS2020/__init__.py.i
data/documentation/inference_example_Prostate.md.i
data/nnunet/evaluation/model_selection/rank_candidates_cascade.py.i
data/nnunet/utilities/__pycache__/to_torch.cpython-39.pyc.i
data/nnunet/evaluation/model_selection/figure_out_what_to_submit.py.i
data/nnunet/dataset_conversion/Task027_AutomaticCardiacDetectionChallenge.py.i
data/nnunet/training/network_training/nnUNet_variants/benchmarking/__init__.py.i
data/documentation/using_nnUNet_as_baseline.md.i
data/nnunet/training/data_augmentation/data_augmentation_insaneDA2.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_CEGDL.py.i
data/nnunet/run/__pycache__/load_pretrained_weights.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_softDeepSupervision.py.i
data/nnunet/training/cascade_stuff/__pycache__/predict_next_stage.cpython-39.pyc.i
data/nnunet/training/dataloading/__init__.py.i
data/nnunet/__pycache__/paths.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/data_augmentation/nnUNetTrainerV2_independentScalePerAxis.py.i
data/nnunet/training/data_augmentation/data_augmentation_noDA.py.i
data/nnunet/training/network_training/nnUNet_variants/nnUNetTrainerCE.py.i
data/nnunet/training/loss_functions/__pycache__/TopK_loss.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_momentum098.py.i
data/nnunet/training/network_training/__pycache__/nnUNetTrainerV2_CascadeFullRes.cpython-39.pyc.i
data/nnunet/experiment_planning/alternative_experiment_planning/experiment_planner_baseline_3DUNet_v21_16GB.py.i
data/nnunet/training/data_augmentation/__pycache__/default_data_augmentation.cpython-39.pyc.i
data/nnunet/experiment_planning/__pycache__/summarize_plans.cpython-39.pyc.i
data/nnunet/experiment_planning/alternative_experiment_planning/normalization/experiment_planner_3DUNet_CT2.py.i
data/nnunet/inference/predict.py.i
data/nnunet.egg-info/top_level.txt.i
data/nnunet/experiment_planning/__pycache__/experiment_planner_baseline_3DUNet_v21.cpython-39.pyc.i
data/nnunet/training/data_augmentation/__pycache__/pyramid_augmentations.cpython-39.pyc.i
data/nnunet/evaluation/model_selection/ensemble.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/__init__.py.i
data/nnunet/evaluation/surface_dice.py.i
data/nnunet/dataset_conversion/Task032_BraTS_2018.py.i
data/nnunet/postprocessing/consolidate_postprocessing.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_Ranger_lr3en4.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_GN.py.i
data/nnunet/utilities/__pycache__/task_name_id_conversion.cpython-39.pyc.i
data/nnunet/experiment_planning/alternative_experiment_planning/pooling_and_convs/experiment_planner_baseline_3DUNet_poolBasedOnSpacing.py.i
data/nnunet/utilities/overlay_plots.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_ResencUNet_DA3.py.i
data/nnunet/network_architecture/__pycache__/initialization.cpython-39.pyc.i
data/nnunet/network_architecture/generic_UNet.py.i
data/nnunet/experiment_planning/__pycache__/experiment_planner_baseline_3DUNet.cpython-39.pyc.i
data/nnunet/training/network_training/network_trainer.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_NoNormalization_lr1en3.py.i
data/nnunet/training/data_augmentation/__pycache__/custom_transforms.cpython-39.pyc.i
data/nnunet/training/data_augmentation/__pycache__/downsampling.cpython-39.pyc.i
data/nnunet/dataset_conversion/Task083_VerSe2020.py.i
data/nnunet/inference/pretrained_models/collect_pretrained_models.py.i
data/nnunet/evaluation/metrics.py.i
data/nnunet/dataset_conversion/Task069_CovidSeg.py.i
data/documentation/tutorials/custom_spacing.md.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_SGD_lrs.py.i
data/nnunet/experiment_planning/__pycache__/experiment_planner_baseline_2DUNet.cpython-39.pyc.i
data/nnunet/evaluation/add_mean_dice_to_json.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/normalization/experiment_planner_3DUNet_nonCT.py.i
data/nnunet/training/loss_functions/deep_supervision.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/patch_size/__init__.py.i
data/nnunet/experiment_planning/old/old_plan_and_preprocess_task.py.i
data/nnunet/dataset_conversion/Task056_VerSe2019.py.i
data/nnunet/network_architecture/custom_modules/__init__.py.i
data/nnunet/network_architecture/generic_UNet_DP.py.i
data/nnunet/training/network_training/competitions_with_custom_Trainers/MMS/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/miscellaneous/nnUNetTrainerV2_fullEvals.py.i
data/nnunet/training/network_training/nnUNet_variants/resampling/__init__.py.i
data/nnunet/training/network_training/competitions_with_custom_Trainers/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_SGD_ReduceOnPlateau.py.i
data/nnunet/network_architecture/generic_modular_residual_UNet.py.i
data/nnunet/experiment_planning/__pycache__/change_batch_size.cpython-39.pyc.i
data/nnunet/training/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/dataset_conversion/Task062_NIHPancreas.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/pooling_and_convs/experiment_planner_baseline_3DUNet_allConv3x3.py.i
data/nnunet/training/optimizer/ranger.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/experiment_planner_baseline_3DUNet_v21_11GB.py.i
data/nnunet/experiment_planning/__pycache__/DatasetAnalyzer.cpython-39.pyc.i
data/nnunet/dataset_conversion/Task076_Fluo_N3DH_SIM.py.i
data/nnunet/utilities/__pycache__/tensor_utilities.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_TopK10.py.i
data/nnunet/postprocessing/consolidate_all_for_paper.py.i
data/nnunet/training/data_augmentation/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/run/__pycache__/run_training.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_3ConvPerStage_samefilters.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_DiceTopK10.py.i
data/nnunet/evaluation/__pycache__/__init__.cpython-39.pyc.i
data/documentation/data_format_inference.md.i
data/nnunet/dataset_conversion/Task029_LiverTumorSegmentationChallenge.py.i
data/nnunet/postprocessing/connected_components.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_fp16.py.i
data/nnunet/configuration.py.i
data/nnunet/utilities/__pycache__/random_stuff.cpython-39.pyc.i
data/nnunet/utilities/__pycache__/one_hot_encoding.cpython-39.pyc.i
data/nnunet/dataset_conversion/Task075_Fluo_C3DH_A549_ManAndSim.py.i
data/nnunet/training/network_training/nnUNetTrainerCascadeFullRes.py.i
data/nnunet/inference/predict_simple.py.i
data/nnunet/training/network_training/nnUNet_variants/cascade/nnUNetTrainerV2CascadeFullRes_shorter.py.i
data/nnunet/experiment_planning/__pycache__/common_utils.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/copies/nnUNetTrainerV2_copies.py.i
data/nnunet/dataset_conversion/Task056_Verse_normalize_orientation.py.i
data/nnunet/dataset_conversion/Task017_BeyondCranialVaultAbdominalOrganSegmentation.py.i
data/nnunet/training/network_training/nnUNet_variants/data_augmentation/nnUNetTrainerV2_DA3.py.i
data/nnunet/training/data_augmentation/data_augmentation_moreDA.py.i
data/nnunet/training/network_training/__pycache__/network_trainer.cpython-39.pyc.i
data/nnunet/experiment_planning/alternative_experiment_planning/target_spacing/experiment_planner_baseline_3DUNet_targetSpacingForAnisoAxis.py.i
data/nnunet/training/network_training/__pycache__/nnUNetTrainerV2.cpython-39.pyc.i
data/nnunet/training/learning_rate/__pycache__/poly_lr.cpython-39.pyc.i
data/documentation/common_questions.md.i
data/nnunet/experiment_planning/alternative_experiment_planning/patch_size/experiment_planner_3DUNet_isotropic_in_voxels.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/normalization/experiment_planner_2DUNet_v21_RGB_scaleto_0_1.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_BN.py.i
data/nnunet/utilities/folder_names.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_SGD_fixedSchedule2.py.i
data/readme.md.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_ReLU_convReLUIN.py.i
data/nnunet/evaluation/__pycache__/evaluator.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_momentum09.py.i
data/nnunet/evaluation/__pycache__/metrics.cpython-39.pyc.i
data/nnunet/training/__pycache__/model_restore.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_allConv3x3.py.i
data/nnunet/preprocessing/cropping.py.i
data/nnunet/training/network_training/nnUNet_variants/copies/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/benchmarking/nnUNetTrainerV2_2epochs.py.i
data/nnunet/evaluation/model_selection/summarize_results_with_plans.py.i
data/documentation/extending_nnunet.md.i
data/nnunet/dataset_conversion/Task061_CREMI.py.i
data/nnunet/training/network_training/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_Ranger_lr1en2.py.i
data/nnunet/training/network_training/nnUNetTrainerV2.py.i
data/nnunet/run/run_training_DDP.py.i
data/nnunet/training/learning_rate/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/preprocessing/custom_preprocessors/preprocessor_scale_RGB_to_0_1.py.i
data/nnunet/evaluation/add_dummy_task_with_mean_over_all_tasks.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_lReLU_biasInSegOutput.py.i
data/nnunet/training/network_training/nnUNetTrainerV2_fp32.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_ReLU.py.i
data/nnunet/run/__pycache__/__init__.cpython-39.pyc.i
data/documentation/tutorials/custom_preprocessing.md.i
data/nnunet/preprocessing/custom_preprocessors/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_CE.py.i
data/nnunet/network_architecture/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/training/dataloading/__pycache__/dataset_loading.cpython-39.pyc.i
data/nnunet/utilities/file_conversions.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/target_spacing/experiment_planner_baseline_3DUNet_v21_noResampling.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_cycleAtEnd.py.i
data/nnunet/training/network_training/__pycache__/nnUNetTrainerCascadeFullRes.cpython-39.pyc.i
data/nnunet/network_architecture/custom_modules/feature_response_normalization.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_Adam.py.i
data/nnunet/evaluation/evaluator.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/patch_size/experiment_planner_3DUNet_isotropic_in_mm.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_ReLU_biasInSegOutput.py.i
data/nnunet/training/network_training/competitions_with_custom_Trainers/BraTS2020/nnUNetTrainerV2BraTSRegions_moreDA.py.i
data/nnunet/paths.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/experiment_planner_baseline_3DUNet_v23.py.i
data/nnunet/training/loss_functions/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_Dice_lr1en3.py.i
data/nnunet/inference/change_trainer.py.i
data/nnunet/dataset_conversion/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_lReLU_convlReLUIN.py.i
data/nnunet/dataset_conversion/Task089_Fluo-N2DH-SIM.py.i
data/nnunet.egg-info/dependency_links.txt.i
data/nnunet/dataset_conversion/Task037_038_Chaos_Challenge.py.i
data/nnunet/preprocessing/sanity_checks.py.i
data/tests/test_steps_for_sliding_window_prediction.py.i
data/nnunet/training/network_training/nnUNetTrainerV2_DDP.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_NoNormalization.py.i
data/documentation/common_problems_and_solutions.md.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_3ConvPerStage.py.i
data/nnunet/experiment_planning/nnUNet_plan_and_preprocess.py.i
data/nnunet/training/network_training/nnUNet_variants/cascade/nnUNetTrainerV2CascadeFullRes_DAVariants.py.i
data/nnunet/inference/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_LReLU_slope_2en1.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_reduceMomentumDuringTraining.py.i
data/nnunet/network_architecture/__pycache__/neural_network.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_ResencUNet.py.i
data/nnunet.egg-info/PKG-INFO.i
data/nnunet.egg-info/SOURCES.txt.i
data/nnunet/inference/__pycache__/segmentation_export.cpython-39.pyc.i
data/nnunet/training/__init__.py.i
data/nnunet/network_architecture/initialization.py.i
data/nnunet/dataset_conversion/Task024_Promise2012.py.i
data/nnunet/utilities/__init__.py.i
data/nnunet/experiment_planning/experiment_planner_baseline_2DUNet_v21.py.i
data/nnunet/training/network_training/nnUNet_variants/cascade/__init__.py.i
data/nnunet/experiment_planning/summarize_plans.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_warmup.py.i
data/nnunet/training/network_training/nnUNetTrainerV2_DP.py.i
data/nnunet/inference/segmentation_export.py.i
data/nnunet/preprocessing/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/training/cascade_stuff/predict_next_stage.py.i
data/nnunet/training/cascade_stuff/__pycache__/__init__.cpython-39.pyc.i
data/HIP_Logo.png.i
data/nnunet/evaluation/model_selection/__init__.py.i
data/nnunet.egg-info/requires.txt.i
data/nnunet/experiment_planning/utils.py.i
data/documentation/expected_epoch_times.md.i
data/nnunet/utilities/tensor_utilities.py.i
data/.github/ISSUE_TEMPLATE/all-issues.md.i
data/nnunet/__pycache__/configuration.cpython-39.pyc.i
data/nnunet/run/__pycache__/default_configuration.cpython-39.pyc.i
data/nnunet/training/data_augmentation/custom_transforms.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_ForceSD.py.i
data/nnunet/training/loss_functions/__pycache__/dice_loss.cpython-39.pyc.i
data/nnunet/network_architecture/__init__.py.i
data/nnunet/dataset_conversion/Task064_KiTS_labelsFixed.py.i
data/nnunet/training/model_restore.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/normalization/__init__.py.i
data/nnunet/training/learning_rate/__init__.py.i
data/nnunet/experiment_planning/change_batch_size.py.i
data/.gitignore.i
data/nnunet/training/network_training/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/experiment_planning/__init__.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/experiment_planner_baseline_3DUNet_v21_32GB.py.i
data/nnunet/training/network_training/nnUNet_variants/cascade/nnUNetTrainerV2CascadeFullRes_lowerLR.py.i
data/nnunet/dataset_conversion/Task035_ISBI_MSLesionSegmentationChallenge.py.i
data/nnunet/network_architecture/custom_modules/mish.py.i
data/nnunet/experiment_planning/__pycache__/experiment_planner_baseline_2DUNet_v21.cpython-39.pyc.i
data/nnunet/inference/__pycache__/__init__.cpython-39.pyc.i
data/setup.cfg.i
data/nnunet/experiment_planning/alternative_experiment_planning/target_spacing/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_SGD_fixedSchedule.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/experiment_planner_baseline_3DUNet_v21_3convperstage.py.i
data/nnunet/evaluation/model_selection/collect_all_fold0_results_and_summarize_in_one_csv.py.i
data/nnunet/utilities/file_endings.py.i
data/.hgignore.i
data/nnunet/experiment_planning/alternative_experiment_planning/experiment_planner_residual_3DUNet_v21.py.i
data/nnunet/utilities/__pycache__/sitk_stuff.cpython-39.pyc.i
data/nnunet/experiment_planning/alternative_experiment_planning/target_spacing/experiment_planner_baseline_3DUNet_v21_customTargetSpacing_2x2x2.py.i
data/nnunet/dataset_conversion/Task055_SegTHOR.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_Adam_lr_3en4.py.i
data/nnunet/training/network_training/nnUNet_variants/benchmarking/nnUNetTrainerV2_dummyLoad.py.i
data/nnunet/run/__init__.py.i
data/nnunet/experiment_planning/old/__init__.py.i
data/nnunet/training/loss_functions/__pycache__/deep_supervision.cpython-39.pyc.i
data/nnunet/preprocessing/__pycache__/preprocessing.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_momentum09in2D.py.i
data/nnunet/training/network_training/competitions_with_custom_Trainers/MMS/nnUNetTrainerV2_MMS.py.i
data/nnunet/preprocessing/preprocessing.py.i
data/nnunet/run/run_training_DP.py.i
data/nnunet/experiment_planning/__pycache__/nnUNet_plan_and_preprocess.cpython-39.pyc.i
data/LICENSE.i
data/nnunet/training/dataloading/dataset_loading.py.i
data/nnunet/inference/pretrained_models/__init__.py.i
data/nnunet/postprocessing/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/run/default_configuration.py.i
data/nnunet/training/network_training/nnUNet_variants/data_augmentation/nnUNetTrainerV2_DA2.py.i
data/nnunet/dataset_conversion/utils.py.i
data/nnunet/utilities/one_hot_encoding.py.i
data/nnunet/__init__.py.i
data/nnunet/network_architecture/custom_modules/helperModules.py.i
data/nnunet/training/network_training/nnUNetTrainer.py.i
data/nnunet/evaluation/model_selection/rank_candidates.py.i
data/nnunet/training/network_training/nnUNet_variants/data_augmentation/nnUNetTrainerV2_noDA.py.i
data/nnunet/training/loss_functions/__init__.py.i
data/nnunet/training/data_augmentation/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_Mish.py.i
data/documentation/training_example_Hippocampus.md.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_MCC.py.i
data/nnunet/dataset_conversion/Task043_BraTS_2019.py.i
data/nnunet/training/network_training/nnUNet_variants/data_augmentation/nnUNetTrainerV2_insaneDA.py.i
data/nnunet/network_architecture/custom_modules/conv_blocks.py.i
data/nnunet/network_architecture/neural_network.py.i
data/nnunet/training/optimizer/__init__.py.i
data/nnunet/utilities/to_torch.py.i
data/nnunet/experiment_planning/nnUNet_convert_decathlon_task.py.i
data/nnunet/experiment_planning/common_utils.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_momentum095.py.i
data/nnunet/dataset_conversion/Task065_KiTS_NicksLabels.py.i
data/nnunet/inference/pretrained_models/download_pretrained_model.py.i
data/nnunet/dataset_conversion/Task082_BraTS_2020.py.i
data/nnunet/postprocessing/__pycache__/connected_components.cpython-39.pyc.i
data/nnunet/training/loss_functions/dice_loss.py.i
data/nnunet/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/evaluation/model_selection/rank_candidates_StructSeg.py.i
data/nnunet/evaluation/region_based_evaluation.py.i
data/nnunet/training/network_training/nnUNet_variants/nnUNetTrainerNoDA.py.i
data/nnunet/training/data_augmentation/pyramid_augmentations.py.i
data/nnunet/dataset_conversion/Task115_COVIDSegChallenge.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_Adam_ReduceOnPlateau.py.i
data/nnunet/preprocessing/__pycache__/cropping.cpython-39.pyc.i
data/nnunet/utilities/distributed.py.i
data/nnunet/training/network_training/nnUNet_variants/miscellaneous/__init__.py.i
data/nnunet/experiment_planning/DatasetAnalyzer.py.i
data/nnunet/training/loss_functions/crossentropy.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/cascade/nnUNetTrainerV2CascadeFullRes_shorter_lowerLR.py.i
data/nnunet.egg-info/entry_points.txt.i
data/nnunet/training/loss_functions/TopK_loss.py.i
data/nnunet/training/data_augmentation/__pycache__/data_augmentation_moreDA.cpython-39.pyc.i
data/nnunet/utilities/sitk_stuff.py.i
data/nnunet/training/data_augmentation/data_augmentation_insaneDA.py.i
data/nnunet/utilities/task_name_id_conversion.py.i
data/nnunet/postprocessing/__init__.py.i
data/setup.py.i
data/nnunet/utilities/nd_softmax.py.i
data/nnunet/experiment_planning/__pycache__/utils.cpython-39.pyc.i
data/nnunet/training/learning_rate/poly_lr.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_noDeepSupervision.py.i
data/nnunet/experiment_planning/experiment_planner_baseline_3DUNet.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_focalLoss.py.i
data/nnunet/dataset_conversion/Task114_heart_MNMs.py.i
data/nnunet/training/loss_functions/__pycache__/crossentropy.cpython-39.pyc.i
data/nnunet/utilities/__pycache__/file_endings.cpython-39.pyc.i
data/nnunet/experiment_planning/alternative_experiment_planning/readme.md.i
data/nnunet/inference/ensemble_predictions.py.i
data/nnunet/experiment_planning/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/training/dataloading/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/dataset_conversion/Task059_EPFL_EM_MITO_SEG.py.i
data/documentation/setting_up_paths.md.i
data/documentation/dataset_conversion.md.i
data/nnunet/evaluation/collect_results_files.py.i
data/nnunet/training/network_training/nnUNetTrainerV2_CascadeFullRes.py.i
data/nnunet/training/network_training/nnUNet_variants/__init__.py.i
data/nnunet/evaluation/model_selection/summarize_results_in_one_json.py.i
data/nnunet/evaluation/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/resampling/nnUNetTrainerV2_resample33.py.i
data/nnunet/training/data_augmentation/downsampling.py.i
data/nnunet/training/network_training/competitions_with_custom_Trainers/BraTS2020/nnUNetTrainerV2BraTSRegions.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_GeLU.py.i
data/nnunet/run/run_training.py.i
data/nnunet/network_architecture/generic_modular_UNet.py.i
data/nnunet/training/network_training/nnUNet_variants/optimizer_and_lr/nnUNetTrainerV2_Ranger_lr3en3.py.i
data/nnunet/experiment_planning/experiment_planner_baseline_3DUNet_v21.py.i
data/nnunet/training/network_training/nnUNet_variants/architectural_variants/nnUNetTrainerV2_ResencUNet_DA3_BN.py.i
data/documentation/tutorials/edit_plans_files.md.i
data/nnunet/preprocessing/__pycache__/sanity_checks.cpython-39.pyc.i
data/nnunet/dataset_conversion/Task120_Massachusetts_RoadSegm.py.i
data/nnunet/training/cascade_stuff/__init__.py.i
data/nnunet/utilities/recursive_rename_taskXX_to_taskXXX.py.i
data/nnunet/utilities/random_stuff.py.i
data/nnunet/training/data_augmentation/default_data_augmentation.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_Dice_squared.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/__init__.py.i
data/nnunet/training/network_training/nnUNet_variants/data_augmentation/nnUNetTrainerV2_noMirroring.py.i
data/nnunet/experiment_planning/alternative_experiment_planning/experiment_planner_baseline_3DUNet_v22.py.i
data/nnunet/training/network_training/nnUNet_variants/loss_function/nnUNetTrainerV2_Loss_Dice.py.i
data/nnunet/dataset_conversion/Task058_ISBI_EM_SEG.py.i
data/nnunet/utilities/recursive_delete_npz.py.i
data/nnunet/dataset_conversion/Task040_KiTS.py.i
data/nnunet/utilities/__pycache__/__init__.cpython-39.pyc.i
data/nnunet/training/network_training/nnUNet_variants/data_augmentation/__init__.py.i
data/nnunet/training/network_training/__pycache__/nnUNetTrainer.cpython-39.pyc.i
data/nnunet/network_architecture/__pycache__/generic_UNet.cpython-39.pyc.i
data/build_nnUNet.cmd.i
data/nnunet.spec.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep2000_nomirror.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_nomirror.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_CascadeFullRes_nomirror.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep2000.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep8000.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep250_nomirror.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep4000_nomirror.py.i
data/build_nnUNet-totalseg.cmd.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep250.py.i
data/nnunet/training/network_training/custom_trainers/__init__.py.i
data/nnunet-totalseg.spec.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_S5_D2_W32_LR_1e4_CropSize_192.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep4000.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep500.py.i
data/nnunet/training/network_training/custom_trainers/nnUNetTrainerV2_ep8000_nomirror.py.i
