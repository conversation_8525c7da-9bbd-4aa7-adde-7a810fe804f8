{"version": "0.2.0", "configurations": [{"name": "nnUNet: Your Custom Prediction", "type": "python", "request": "launch", "module": "nnunet.inference.predict_simple", "args": ["-i", "C:/test/nnunet/Task851-MNI-MR2Brain/predict/Input/", "-o", "C:/test/nnunet/Task851-MNI-MR2Brain/predict/Output/", "-t", "851", "--mode", "normal", "--disable_tta", "--overwrite_existing", "-chk", "model_best", "-tr", "nnUNetTrainerV2_ep4000_nomirror"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"nnUNet_raw_data_base": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_raw", "nnUNet_preprocessed": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_preprocessed", "RESULTS_FOLDER": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_trained_models", "KMP_DUPLICATE_LIB_OK": "TRUE"}, "python": "${workspaceFolder}/nnunet-gpu-env/Scripts/python.exe"}, {"name": "nnUNet: Train Model (Updated Paths)", "type": "python", "request": "launch", "module": "nnunet.run.run_training", "args": ["3d_fullres", "nnUNetTrainerV2", "Task851", "0"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"nnUNet_raw_data_base": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_raw", "nnUNet_preprocessed": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_preprocessed", "RESULTS_FOLDER": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_trained_models", "KMP_DUPLICATE_LIB_OK": "TRUE"}, "python": "${workspaceFolder}/nnunet-gpu-env/Scripts/python.exe"}, {"name": "nnUNet: Plan and Preprocess (Updated Paths)", "type": "python", "request": "launch", "module": "nnunet.experiment_planning.nnUNet_plan_and_preprocess", "args": ["-t", "851"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"nnUNet_raw_data_base": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_raw", "nnUNet_preprocessed": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_preprocessed", "RESULTS_FOLDER": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_trained_models", "KMP_DUPLICATE_LIB_OK": "TRUE"}, "python": "${workspaceFolder}/nnunet-gpu-env/Scripts/python.exe"}, {"name": "nnUNet: Debug Current File (Updated Paths)", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"nnUNet_raw_data_base": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_raw", "nnUNet_preprocessed": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_preprocessed", "RESULTS_FOLDER": "C:/ARTDaemon/distribute/nnUNETData.segman_MR/nnUNet_trained_models", "KMP_DUPLICATE_LIB_OK": "TRUE"}, "python": "${workspaceFolder}/nnunet-gpu-env/Scripts/python.exe"}]}